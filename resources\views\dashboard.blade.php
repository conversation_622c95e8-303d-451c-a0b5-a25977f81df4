<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800   leading-tight">
            {{ __('Restricted IPs') }}
        </h2>
    </x-slot>

    <div class="py-6 max-w-7xl mx-auto sm:px-6 lg:px-8">
        {{-- Add New IP Modal Trigger --}}
        <x-restrict-ip.create-modal />

        {{-- Flash Message --}}
        @if (session('success'))
            <div class="mt-4 p-3 rounded-md bg-green-100 text-green-700">
                {{ session('success') }}
            </div>
        @endif



        {{-- IP Table --}}
        <div class="mt-6 overflow-x-auto bg-white   shadow-md sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300  ">
                <thead class="bg-gray-50  ">
                    <tr>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">IP</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Created By</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Status</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Comment</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Created At</th>
                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700  ">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200  ">
                    @foreach ($ips as $ip)
                        <tr>
                            <td class="px-4 py-2 font-mono text-gray-900  ">{{ $ip->ip }}</td>
                            <td class="px-4 py-2 text-gray-800  ">{{ $ip->user_name  }}</td>
                            <td class="px-4 py-2">
                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                    {{ $ip->status ? 'bg-green-100 text-green-800' : 'bg-gray-200 text-gray-700' }}">
                                    {{ $ip->status ? 'Active' : 'Not Active' }}
                                </span>
                            </td>
                            {{-- <td class="px-4 py-2">
                                <div x-data="{ checked: {{ $ip->status ? 'true' : 'false' }} }">
                                    <label class="inline-flex items-center gap-2 cursor-pointer">
                                        <input
                                            type="checkbox"
                                            class="form-checkbox text-green-600 focus:ring-green-500"
                                            :checked="checked"
                                            @change="
                                                checked = !checked;
                                                fetch('{{ route('restrict-ips.toggle-status', $ip->id) }}', {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                                    },
                                                    body: JSON.stringify({ status: checked })
                                                });
                                            "
                                        >
                                        <span
                                            class="text-xs font-medium"
                                            :class="checked ? 'text-green-800' : 'text-gray-500'"
                                            x-text="checked ? 'Active' : 'Inactive'"
                                        ></span>
                                    </label>
                                </div>
                            </td> --}}
                            <td class="px-4 py-2 text-gray-800  ">{{ $ip->comment }}</td>
                            <td class="px-4 py-2 text-sm text-gray-500  ">{{ $ip->created_at->format('Y-m-d H:i') }}</td>
                            <td class="px-4 py-2 text-right">
                                <x-confirm-delete :action="route('restrict-ips.destroy', $ip->id)" />
                            </td>
                        </tr>
                    @endforeach

                    @if ($ips->isEmpty())
                        <tr>
                            <td colspan="6" class="px-4 py-4 text-center text-gray-500  ">
                                No restricted IPs found.
                            </td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</x-app-layout>
