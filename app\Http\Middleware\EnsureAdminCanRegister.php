<?php

// app/Http/Middleware/EnsureAdminCanRegister.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class EnsureAdminCanRegister
{
    public function handle(Request $request, Closure $next): Response
    {
        // Allow if no users exist (first launch / seeder hasn’t run)
        if (User::count() === 0) {
            return $next($request);
        }

        // Must be logged in AND a super-admin
        if (Auth::check() && Auth::user()->role === 'super-admin') {
            return $next($request);
        }

        abort(403, 'Registration is restricted.');
    }
}