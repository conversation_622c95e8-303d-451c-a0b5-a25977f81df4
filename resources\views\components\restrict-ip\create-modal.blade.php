<!-- Create-IP Modal component  -->
<div x-data="{ open: @json($errors->any()) }" class="relative">
    <!-- Trigger button -->
    <button @click="open = true"
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
        Create New IP
    </button>

    <!-- Backdrop -->
    <div x-show="open" x-transition
         class="fixed inset-0 bg-black/50 flex items-center justify-center"
         style="display:none">
        <!-- Panel -->
        <div @click.away="open = false"
             class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 class="text-xl font-semibold mb-4 text-gray-900">Add New Restricted IP</h2>

            <form method="POST" action="{{ route('restrict-ips.store') }}">
                @csrf

                <!-- IP as 4 numeric boxes -->
                <div x-data="{
                        o1:'{{ old('ip') ? explode('.',old('ip'))[0] ?? '' : '' }}',
                        o2:'{{ old('ip') ? explode('.',old('ip'))[1] ?? '' : '' }}',
                        o3:'{{ old('ip') ? explode('.',old('ip'))[2] ?? '' : '' }}',
                        o4:'{{ old('ip') ? explode('.',old('ip'))[3] ?? '' : '' }}',
                        setIp(){ $refs.full.value=[this.o1,this.o2,this.o3,this.o4].join('.'); }
                    }"
                    x-init="setIp()" class="mb-4">
                    <label class="block mb-1 text-gray-700">IP Address</label>

                    <div class="flex items-center gap-1">
                        <!-- Octet 1 -->
                        <input x-model="o1" x-ref="o1"
                               @input="
                                   o1=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o1.length===3)$refs.o2.focus(); setIp();"
                               @keydown.backspace="if(!o1)$refs.o1.blur()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring @error('ip') border-red-500 @else border-gray-300 @enderror"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 2 -->
                        <input x-model="o2" x-ref="o2"
                               @input="
                                   o2=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o2.length===3)$refs.o3.focus(); setIp();"
                               @keydown.backspace="if(!o2)$refs.o1.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring @error('ip') border-red-500 @else border-gray-300 @enderror"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 3 -->
                        <input x-model="o3" x-ref="o3"
                               @input="
                                   o3=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o3.length===3)$refs.o4.focus(); setIp();"
                               @keydown.backspace="if(!o3)$refs.o2.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring @error('ip') border-red-500 @else border-gray-300 @enderror"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 4 -->
                        <input x-model="o4" x-ref="o4"
                               @input="
                                   o4=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   setIp();"
                               @keydown.backspace="if(!o4)$refs.o3.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring @error('ip') border-red-500 @else border-gray-300 @enderror"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                    </div>

                    <!-- Hidden full IP -->
                    <input type="hidden" name="ip" x-ref="full" required>

                    @error('ip')
                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Comment -->
                <div class="mb-4">
                    <label for="comment" class="block mb-1 text-gray-700">Comment (optional)</label>
                    <textarea id="comment" name="comment" rows="3"
                              class="w-full px-3 py-2 border rounded focus:outline-none focus:ring
                              @error('comment') border-red-500 @else border-gray-300 @enderror"
                              placeholder="Add a comment...">{{ old('comment') }}</textarea>
                    @error('comment')
                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Buttons -->
                <div class="flex justify-end gap-3">
                    <button type="button" @click="open=false"
                            class="px-4 py-2 rounded border border-gray-300 hover:bg-gray-100">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include Alpine once -->
<script src="//unpkg.com/alpinejs" defer></script>
