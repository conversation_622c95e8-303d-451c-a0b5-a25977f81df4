<section class="space-y-6">
    <header>
        <h2 class="text-lg font-medium text-gray-900  ">
            {{ __('Create New User') }}
        </h2>
        <p class="mt-1 text-sm text-gray-600  ">
            {{ __('As a super-admin, you can create new user accounts here.') }}
        </p>
    </header>

    @if(session('success'))
        <div class="mb-4 text-green-600">{{ session('success') }}</div>
    @endif

    <form method="POST" action="{{ route('profile.store-user') }}">
        @csrf

        <!-- Name -->
        <div>
            <x-input-label for="name" value="Name" />
            <x-text-input id="name" class="mt-1 block w-full"  name="name" required autofocus />
            <x-input-error :messages="$errors->get('name')" />
        </div>

        <!-- Email -->
        <div class="mt-4">
            <x-input-label for="email" value="Email" />
            <x-text-input id="email" name="email" class="mt-1 block w-full"  type="email" required />
            <x-input-error :messages="$errors->get('email')" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <x-input-label for="password" value="Password" />
            <x-text-input id="password" name="password" class="mt-1 block w-full"  type="password" required autocomplete="new-password" />
            <x-input-error :messages="$errors->get('password')" />
        </div>

        <!-- Confirm Password -->
        <div class="mt-4">
            <x-input-label for="password_confirmation" value="Confirm Password" />
            <x-text-input id="password_confirmation" class="mt-1 block w-full"  name="password_confirmation" type="password" required />
            <x-input-error :messages="$errors->get('password_confirmation')" />
        </div>

        <div class="mt-6 flex justify-start">
            <x-primary-button>{{ __('Create User') }}</x-primary-button>
        </div>
    </form>
</section>
