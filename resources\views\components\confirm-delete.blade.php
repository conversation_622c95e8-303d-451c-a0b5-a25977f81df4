@props([
    'action',        // route string
    'title' => 'Delete IP',
    'message' => 'Are you sure you want to delete this IP? This action cannot be undone.',
])

<div x-data="{ open: false }" x-cloak>
    <!-- Trigger -->
    <button
        type="button"
        @click="open = true"
        class="rounded-md bg-red-600 px-3 py-1.5 text-sm text-white shadow hover:bg-red-700"
    >
        Delete
    </button>

    <!-- Backdrop -->
    <div
        x-show="open"
        x-transition.opacity
        class="fixed inset-0 z-40 bg-black/40"
    ></div>

    <!-- Modal -->
    <div
        x-show="open"
        x-transition
        class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
        <div
            @click.outside="open = false"
            class="w-full max-w-md rounded-xl bg-white p-6 shadow-xl"
        >
            <h2 class="text-lg  font-semibold text-start text-gray-900">{{ $title }}</h2>
            <p class="mt-2 text-start text-sm text-gray-600">{{ $message }}</p>

            <div class="mt-6 flex justify-end space-x-3">
                <button
                    type="button"
                    @click="open = false"
                    class="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
                >
                    Cancel
                </button>

                <form method="POST" action="{{ $action }}">
                    @csrf
                    @method('DELETE')
                    <button
                        type="submit"
                        class="rounded-md bg-red-600 px-4 py-2 font-medium text-white hover:bg-red-700"
                    >
                        Confirm
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
