<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'action',        // route string
    'title' => 'Delete IP',
    'message' => 'Are you sure you want to delete this IP? This action cannot be undone.',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'action',        // route string
    'title' => 'Delete IP',
    'message' => 'Are you sure you want to delete this IP? This action cannot be undone.',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="{ open: false }" x-cloak>
    <!-- Trigger -->
    <button
        type="button"
        @click="open = true"
        class="rounded-md bg-red-600 px-3 py-1.5 text-sm text-white shadow hover:bg-red-700"
    >
        Delete
    </button>

    <!-- Backdrop -->
    <div
        x-show="open"
        x-transition.opacity
        class="fixed inset-0 z-40 bg-black/40"
    ></div>

    <!-- Modal -->
    <div
        x-show="open"
        x-transition
        class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
        <div
            @click.outside="open = false"
            class="w-full max-w-md rounded-xl bg-white p-6 shadow-xl"
        >
            <h2 class="text-lg  font-semibold text-start text-gray-900"><?php echo e($title); ?></h2>
            <p class="mt-2 text-start text-sm text-gray-600"><?php echo e($message); ?></p>

            <div class="mt-6 flex justify-end space-x-3">
                <button
                    type="button"
                    @click="open = false"
                    class="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
                >
                    Cancel
                </button>

                <form method="POST" action="<?php echo e($action); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button
                        type="submit"
                        class="rounded-md bg-red-600 px-4 py-2 font-medium text-white hover:bg-red-700"
                    >
                        Confirm
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Coding\COD\restrict-ip-app\resources\views/components/confirm-delete.blade.php ENDPATH**/ ?>