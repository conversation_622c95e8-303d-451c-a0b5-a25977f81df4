<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RestrictIp;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RestrictIpApiController extends Controller
{
    public function activeIps(Request $request): JsonResponse
    {
        $token = $request->header('X-API-TOKEN');

        if (!$token || $token !== env('API_ACCESS_TOKEN')) {
            return response()->json([
                'response' => 'error',
                'message' => 'Unauthorized. Invalid or missing API token.'
            ], 401);
        }

        // Get all active IPs (status = 1)
        $activeIps = RestrictIp::where('status', true)->get();

        return response()->json([
            'response' => 'success',
            'result' => $activeIps
        ]);
    }
}