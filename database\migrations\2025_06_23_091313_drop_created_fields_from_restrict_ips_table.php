<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('restrict_ips', function (Blueprint $table) {
            $table->dropColumn(['created_id', 'created_name']);
        });
    }

    public function down(): void
    {
        Schema::table('restrict_ips', function (Blueprint $table) {
            $table->unsignedBigInteger('created_id')->nullable();
            $table->string('created_name')->nullable();
        });
    }
};