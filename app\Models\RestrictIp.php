<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class RestrictIp extends Model
{
    use HasFactory;

   /** mass-assignable columns */
   protected $fillable = [
    'ip',
    'status',
    'comment',
    'user_id',
    'user_name',
    'is_archived',
];

/** casts */
protected $casts = [
    'status'      => 'boolean',
    'is_archived' => 'boolean',
    'created_at'  => 'datetime',
    'updated_at'  => 'datetime',
];

    /**
     * Owner of the IP (user it belongs to).
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'user_id');
    }



    public function scopeActive($query)
    {
        return $query->where('status', true)->where('is_archived', false);
    }


    /**
     * Always store IP in canonical format.
     */
    protected function ip(): Attribute
    {
        return Attribute::make(
            set: fn (string $value) => trim($value)
        );
    }
}