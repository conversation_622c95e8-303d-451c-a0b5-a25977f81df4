<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800   leading-tight">
            <?php echo e(__('Restricted IPs')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-6 max-w-7xl mx-auto sm:px-6 lg:px-8">
        
        <?php if (isset($component)) { $__componentOriginalcff5485d51986c6045ef29989c8e8845 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcff5485d51986c6045ef29989c8e8845 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.restrict-ip.create-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('restrict-ip.create-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcff5485d51986c6045ef29989c8e8845)): ?>
<?php $attributes = $__attributesOriginalcff5485d51986c6045ef29989c8e8845; ?>
<?php unset($__attributesOriginalcff5485d51986c6045ef29989c8e8845); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcff5485d51986c6045ef29989c8e8845)): ?>
<?php $component = $__componentOriginalcff5485d51986c6045ef29989c8e8845; ?>
<?php unset($__componentOriginalcff5485d51986c6045ef29989c8e8845); ?>
<?php endif; ?>

        
        <?php if(session('success')): ?>
            <div class="mt-4 p-3 rounded-md bg-green-100 text-green-700">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>



        
        <div class="mt-6 overflow-x-auto bg-white   shadow-md sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300  ">
                <thead class="bg-gray-50  ">
                    <tr>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">IP</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Created By</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Status</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Comment</th>
                        <th class="px-4 py-2 text-left text-sm font-medium text-gray-700  ">Created At</th>
                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700  ">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200  ">
                    <?php $__currentLoopData = $ips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="px-4 py-2 font-mono text-gray-900  "><?php echo e($ip->ip); ?></td>
                            <td class="px-4 py-2 text-gray-800  "><?php echo e($ip->user_name); ?></td>
                            <td class="px-4 py-2">
                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                    <?php echo e($ip->status ? 'bg-green-100 text-green-800' : 'bg-gray-200 text-gray-700'); ?>">
                                    <?php echo e($ip->status ? 'Active' : 'Not Active'); ?>

                                </span>
                            </td>
                            
                            <td class="px-4 py-2 text-gray-800  "><?php echo e($ip->comment); ?></td>
                            <td class="px-4 py-2 text-sm text-gray-500  "><?php echo e($ip->created_at->format('Y-m-d H:i')); ?></td>
                            <td class="px-4 py-2 text-right">
                                <?php if (isset($component)) { $__componentOriginal31d8dad53715b0a3883f430a1e78226c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal31d8dad53715b0a3883f430a1e78226c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.confirm-delete','data' => ['action' => route('restrict-ips.destroy', $ip->id)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('confirm-delete'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('restrict-ips.destroy', $ip->id))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal31d8dad53715b0a3883f430a1e78226c)): ?>
<?php $attributes = $__attributesOriginal31d8dad53715b0a3883f430a1e78226c; ?>
<?php unset($__attributesOriginal31d8dad53715b0a3883f430a1e78226c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal31d8dad53715b0a3883f430a1e78226c)): ?>
<?php $component = $__componentOriginal31d8dad53715b0a3883f430a1e78226c; ?>
<?php unset($__componentOriginal31d8dad53715b0a3883f430a1e78226c); ?>
<?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php if($ips->isEmpty()): ?>
                        <tr>
                            <td colspan="6" class="px-4 py-4 text-center text-gray-500  ">
                                No restricted IPs found.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Coding\COD\restrict-ip-app\resources\views/dashboard.blade.php ENDPATH**/ ?>