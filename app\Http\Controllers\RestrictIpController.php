<?php

namespace App\Http\Controllers;

use App\Models\RestrictIp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RestrictIpController extends Controller
{
    /**
     * Display a listing of the restricted IPs (dashboard).
     */
    public function index()
    {
        $ips = RestrictIp::where('is_archived', false)->orderBy('created_at', 'desc')
        ->get();
        return view('dashboard', compact('ips'));
    }

    /**
     * Store a newly created restricted IP.
     */
    public function store(Request $request)
    {
        $request->validate([
            'ip' => 'required|ip',
            'comment' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();

        RestrictIp::create([
            'ip' => $request->ip,
            'status' => 1,
            'comment' => $request->comment,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'created_id' => $user->id,
            'created_name' => $user->name,
        ]);

        return redirect()->route('dashboard')->with('success', 'IP address added successfully.');
    }

    /**
     * Remove the specified restricted IP from storage.
     */
    public function destroy(RestrictIp $restrictIp)
    {
        $restrictIp->update(['is_archived' => true, 'status' => false]);

        return redirect()->route('dashboard')->with('success', 'IP address deleted successfully.');
    }

    public function toggleStatus(Request $request, RestrictIp $restrict_ip): \Illuminate\Http\JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|boolean',
        ]);

        $restrict_ip->status = $validated['status'];
        $restrict_ip->save();

        return response()->json(['success' => true, 'status' => $restrict_ip->status]);
    }
}