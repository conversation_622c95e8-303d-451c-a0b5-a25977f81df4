<!-- Create-IP Modal component  -->
<div x-data="{ open: <?php echo json_encode($errors->any(), 15, 512) ?> }" class="relative">
    <!-- Trigger button -->
    <button @click="open = true"
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
        Create New IP
    </button>

    <!-- Backdrop -->
    <div x-show="open" x-transition
         class="fixed inset-0 bg-black/50 flex items-center justify-center"
         style="display:none">
        <!-- Panel -->
        <div @click.away="open = false"
             class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 class="text-xl font-semibold mb-4 text-gray-900">Add New Restricted IP</h2>

            <form method="POST" action="<?php echo e(route('restrict-ips.store')); ?>">
                <?php echo csrf_field(); ?>

                <!-- IP as 4 numeric boxes -->
                <div x-data="{
                        o1:'<?php echo e(old('ip') ? explode('.',old('ip'))[0] ?? '' : ''); ?>',
                        o2:'<?php echo e(old('ip') ? explode('.',old('ip'))[1] ?? '' : ''); ?>',
                        o3:'<?php echo e(old('ip') ? explode('.',old('ip'))[2] ?? '' : ''); ?>',
                        o4:'<?php echo e(old('ip') ? explode('.',old('ip'))[3] ?? '' : ''); ?>',
                        setIp(){ $refs.full.value=[this.o1,this.o2,this.o3,this.o4].join('.'); }
                    }"
                    x-init="setIp()" class="mb-4">
                    <label class="block mb-1 text-gray-700">IP Address</label>

                    <div class="flex items-center gap-1">
                        <!-- Octet 1 -->
                        <input x-model="o1" x-ref="o1"
                               @input="
                                   o1=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o1.length===3)$refs.o2.focus(); setIp();"
                               @keydown.backspace="if(!o1)$refs.o1.blur()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring <?php $__errorArgs = ['ip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php else: ?> border-gray-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 2 -->
                        <input x-model="o2" x-ref="o2"
                               @input="
                                   o2=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o2.length===3)$refs.o3.focus(); setIp();"
                               @keydown.backspace="if(!o2)$refs.o1.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring <?php $__errorArgs = ['ip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php else: ?> border-gray-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 3 -->
                        <input x-model="o3" x-ref="o3"
                               @input="
                                   o3=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   if(o3.length===3)$refs.o4.focus(); setIp();"
                               @keydown.backspace="if(!o3)$refs.o2.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring <?php $__errorArgs = ['ip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php else: ?> border-gray-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                        <span class="text-gray-500 select-none">.</span>

                        <!-- Octet 4 -->
                        <input x-model="o4" x-ref="o4"
                               @input="
                                   o4=$event.target.value.replace(/\D/g,'').slice(0,3);
                                   setIp();"
                               @keydown.backspace="if(!o4)$refs.o3.focus()"
                               class="w-14 px-2 py-1 border rounded text-center focus:outline-none focus:ring <?php $__errorArgs = ['ip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php else: ?> border-gray-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               type="text" inputmode="numeric" maxlength="3" placeholder="0">
                    </div>

                    <!-- Hidden full IP -->
                    <input type="hidden" name="ip" x-ref="full" required>

                    <?php $__errorArgs = ['ip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Comment -->
                <div class="mb-4">
                    <label for="comment" class="block mb-1 text-gray-700">Comment (optional)</label>
                    <textarea id="comment" name="comment" rows="3"
                              class="w-full px-3 py-2 border rounded focus:outline-none focus:ring
                              <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php else: ?> border-gray-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              placeholder="Add a comment..."><?php echo e(old('comment')); ?></textarea>
                    <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Buttons -->
                <div class="flex justify-end gap-3">
                    <button type="button" @click="open=false"
                            class="px-4 py-2 rounded border border-gray-300 hover:bg-gray-100">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include Alpine once -->
<script src="//unpkg.com/alpinejs" defer></script>
<?php /**PATH C:\Users\<USER>\Coding\COD\restrict-ip-app\resources\views/components/restrict-ip/create-modal.blade.php ENDPATH**/ ?>