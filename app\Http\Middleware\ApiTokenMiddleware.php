<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiTokenMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header('X-API-TOKEN');
        if (!$token || $token !== env('API_ACCESS_TOKEN')) {
            return response()->json([
                'response' => 'error',
                'message' => 'Unauthorized. Invalid or missing API token.'
            ], 401);
        }
        return $next($request);
    }
}