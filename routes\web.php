<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RestrictIpController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', [RestrictIpController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');
Route::middleware('auth')->group(function () {
    Route::resource('restrict-ips', RestrictIpController::class)->only(['store', 'destroy']);
    Route::post('/restrict-ips/{restrict_ip}/toggle-status', [RestrictIpController::class, 'toggleStatus'])
    ->name('restrict-ips.toggle-status');


    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/create-user', [ProfileController::class, 'createUserForm'])->name('profile.create-user'); // optional if you want separate page
    Route::post('/profile/create-user', [ProfileController::class, 'storeUser'])->name('profile.store-user');
});

require __DIR__.'/auth.php';