<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pull desired credentials from .env (with sensible defaults)
        $email    = env('ADMIN_EMAIL', '<EMAIL>');
        $password = env('ADMIN_PASSWORD', 'ChangeMe123!');
        $name     = env('ADMIN_NAME', 'lyousfi');

        // Does a super-admin (by role) OR this e-mail already exist?
        $superAdminExists = User::where('role', 'super-admin')->exists();
        $emailExists      = User::where('email', $email)->exists();

        if (! $superAdminExists && ! $emailExists) {

            User::create([
                'name'     => $name,
                'email'    => $email,
                'password' => Hash::make($password),
                'role'     => 'super-admin',
            ]);

            $this->command->info("✅  Super-admin created (email: $email)");
        } else {
            $this->command->warn('ℹ️  Super-admin NOT created:');
            if ($superAdminExists) {
                $this->command->line('   • A user with role super-admin already exists.');
            }
            if ($emailExists) {
                $this->command->line("   • The e-mail $email is already in use.");
            }
        }
    }
}